# Redis 管理控制台

这是一个优化的Redis管理和监控控制台应用，提供自动化的Redis进程管理、健康监控、性能优化和备份功能。

## 🚀 功能特性

### 核心功能
- **🔄 自动进程管理**: 自动启动、监控和重启Redis服务器
- **💓 实时健康监控**: 监控内存使用、连接数、CPU使用率等关键指标
- **⚡ 性能优化**: 自动调整Redis配置以获得最佳性能
- **💾 自动备份**: 定期备份Redis数据
- **🔧 故障恢复**: 自动检测和恢复Redis故障
- **📝 优化日志记录**: 中文界面，表情符号增强，便于阅读

### 监控指标
- 内存使用情况和内存策略
- 活跃连接数和连接池状态
- CPU使用率和性能指标
- 缓存命中率和键空间统计
- 持久化状态和备份状态
- 慢查询日志和性能分析

### 自动优化
- 内存管理和淘汰策略
- 连接池和超时设置
- 持久化配置优化
- 慢查询日志配置
- 数据结构优化设置

## 🎯 控制台应用特性

- **友好的中文界面**: 所有日志消息都使用中文显示
- **表情符号增强**: 使用表情符号让日志更加直观易读
- **交互式控制**: 支持按键退出（'q' 键或 Ctrl+C）
- **实时状态显示**: 实时显示Redis服务状态和监控信息
- **优化的控制台输出**: 彩色日志输出，提升用户体验

## 🛠️ 使用方法

### 启动应用

#### 方法1: 使用启动脚本
```bash
# 双击运行 start.bat 文件
start.bat
```

#### 方法2: 使用 dotnet 命令
```bash
# 在项目目录中运行
dotnet run

# 或者指定项目文件
dotnet run --project RunRedis.csproj
```

#### 方法3: 运行编译后的可执行文件
```bash
# 先编译项目
dotnet build --configuration Release

# 运行编译后的文件
.\bin\Release\netcoreapp3.1\SOMS内存数据库.exe
```

### 退出应用

- **按 'q' 键**: 优雅退出程序
- **按 Ctrl+C**: 强制退出程序
- **关闭控制台窗口**: 直接关闭

### 日志输出示例

```
[09:34:29 INF] === Redis 管理控制台启动 ===
[09:34:29 INF] 当前工作目录: D:\Project\SOMS\server\src\YunDa.Quick\RunRedis
[09:34:29 INF] 按 'Ctrl+C' 或 'q' 键退出程序
[09:34:29 INF] 配置加载完成 - Redis服务器: 127.0.0.1:36379
[09:34:29 INF] 🚀 启动 Redis 增强监控和管理服务
[09:34:29 INF] 🔧 执行 Redis 初始设置和验证
[09:34:29 INF] 🔍 验证 Redis 配置
[09:34:29 INF] ✅ 配置验证通过 - 主机: 127.0.0.1:36379
[09:34:29 INF] ✅ Redis 服务器已在运行
[09:34:30 INF] ✅ Redis 健康检查通过
[09:34:30 INF] 🔧 执行初始性能优化...
[09:34:30 INF] ✅ 初始设置完成
[09:34:30 INF] 🔄 启动 Redis 监控循环 (检查间隔: 3秒)
```

## ⚙️ 配置说明

### 主要配置项 (appsettings.json)

```json
{
  "RedisSetting": {
    "Host": "127.0.0.1",
    "Port": "36379",
    "Auth": "yunda123",
    "ProcessCheckIntervalSeconds": 3,
    "HealthCheckIntervalSeconds": 30,
    "EnablePerformanceOptimization": true,
    "EnableAutoBackup": true
  }
}
```

### 关键配置说明

- **Host/Port**: Redis服务器地址和端口
- **ProcessCheckIntervalSeconds**: 进程检查间隔（秒）
- **HealthCheckIntervalSeconds**: 健康检查间隔（秒）
- **EnablePerformanceOptimization**: 是否启用性能优化
- **EnableAutoBackup**: 是否启用自动备份

## 📁 文件结构

```
RunRedis/
├── Program.cs              # 主程序入口
├── Worker.cs               # 工作进程逻辑
├── RedisSetting.cs         # 配置类
├── appsettings.json        # 配置文件
├── start.bat               # 启动脚本
├── Services/               # 服务类
│   ├── RedisProcessManager.cs
│   ├── RedisHealthMonitor.cs
│   └── RedisPerformanceOptimizer.cs
└── Models/                 # 模型类
    └── HealthCheckResult.cs
```

## 🔧 故障排除

### 常见问题

1. **Redis无法启动**
   - 检查Redis可执行文件路径
   - 验证端口是否被占用
   - 检查数据目录权限

2. **配置文件找不到**
   - 确保appsettings.json在正确位置
   - 检查文件权限

3. **性能优化失败**
   - 检查Redis CLI工具路径
   - 验证Redis连接权限

### 日志文件位置

- **控制台输出**: 实时显示在控制台
- **文件日志**: `D:\SOMS\Logs\RunRedis-{日期}.txt`

## 🆚 与服务版本的区别

| 特性 | 控制台版本 | 服务版本 |
|------|------------|----------|
| 运行方式 | 控制台应用 | Windows服务 |
| 用户界面 | 实时控制台输出 | 仅日志文件 |
| 交互性 | 支持按键退出 | 服务管理器控制 |
| 日志显示 | 中文+表情符号 | 英文日志 |
| 启动方式 | 手动启动 | 自动启动 |
| 适用场景 | 开发调试 | 生产环境 |

## 📝 更新日志

### v2.0 (控制台版本)
- ✅ 改为控制台应用模式
- ✅ 优化日志输出，使用中文和表情符号
- ✅ 添加交互式退出功能
- ✅ 改进用户体验
- ✅ 简化启动流程

### v1.0 (服务版本)
- ✅ Windows服务模式
- ✅ 基础监控功能
- ✅ 性能优化
- ✅ 自动备份
