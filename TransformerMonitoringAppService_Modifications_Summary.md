# TransformerMonitoringAppService 遥测存储系统集成修改总结

## 修改概述

本次修改将 `TransformerMonitoringAppService` 的数据源实现从简单的 Redis 查询和模拟数据生成，升级为使用完整的遥测存储系统架构，包括分桶存储、统计数据和多数据源支持。

## 主要变更

### 1. 依赖注入更新

**新增依赖项：**
- `ITelemeteringBucketQueryService _bucketQueryService` - 遥测分桶查询服务
- `IMongoDbRepository<TelemeteringResult, Guid> _telemeteringResultRepository` - 遥测结果仓储
- `IMongoDbRepository<TelemeteringStatisticsResult, Guid> _statisticsRepository` - 遥测统计结果仓储

**新增命名空间：**
- `Yunda.SOMS.MongoDB.Entities.DataMonitoring`
- `Yunda.ISAS.MongoDB.Entities.DataMonitoring`

### 2. 数据检索逻辑重构

#### 原有实现问题：
- 硬编码 Redis 键 `"telemeteringModelList_Zongzi"`
- 仅支持实时数据，无历史数据查询
- 大量依赖模拟数据生成
- 未利用遥测存储系统的分桶和统计功能

#### 新实现特性：
- **智能数据源选择**：根据查询类型自动选择最适合的数据源
- **多层数据架构**：支持实时数据、分桶存储、统计数据
- **时间范围优化**：根据查询时间跨度选择最优存储策略
- **错误处理增强**：完善的异常处理和降级机制

### 3. 方法级别修改

#### 所有主要方法的统一更新：
- `GetHighSidePhaseVoltage()` - 高压侧相电压
- `GetHighSidePhaseCurrent()` - 高压侧相电流  
- `GetLowSideLineVoltage()` - 低压侧线电压
- `GetLowSideLineCurrent()` - 低压侧线电流

**修改模式：**
```csharp
// 原有代码
var redisKey = "telemeteringModelList_Zongzi";
var telemeterings = await _telemeteringModelListRedis.HashSetGetAllAsync(redisKey);
phaseAVoltageData = await GenerateVoltageData(configs, telemeterings, ...);

// 新代码
DateTime startTime = currentTime.AddSeconds(-1 * (pointCount - 1) * interval.TotalSeconds);
DateTime endTime = currentTime;
phaseAVoltageData = await GetTelemetryVoltageData(configs, startTime, endTime, ...);
```

### 4. 新增核心方法

#### `GetTelemetryDataByType()` - 智能数据源选择
根据查询类型自动选择数据源：
- **实时查询** → Redis 缓存
- **小时/日查询** → 分桶存储 (TelemeteringBucket)
- **周/月/年查询** → 统计数据 (TelemeteringStatisticsBucket)

#### `GetRealTimeDataFromRedis()` - Redis 实时数据获取
优化的 Redis 数据查询，支持单点遥测数据获取

#### `GetStatisticsData()` - 统计数据查询
利用遥测存储系统的统计功能，支持多种统计类型：
- 平均值 (Average)
- 最大值 (Maximum) 
- 最小值 (Minimum)
- 实时值 (RealTime)

#### `MapPowerTypeToInterval()` - 查询类型映射
将业务查询类型映射到存储系统的时间间隔：
```csharp
RealTimePowerTypeEnum.Hourly → FixedIntervalEnum.Minute5
RealTimePowerTypeEnum.Daily → FixedIntervalEnum.Hour1
RealTimePowerTypeEnum.Weekly → FixedIntervalEnum.Day1
RealTimePowerTypeEnum.Monthly → FixedIntervalEnum.Day1
RealTimePowerTypeEnum.Yearly → FixedIntervalEnum.Day30
```

#### `ProcessRealTelemetryData()` - 实际数据处理
处理从遥测存储系统获取的真实数据：
- 时间序列对齐
- 数据插值和近似
- 格式化输出

### 5. 数据源架构对比

#### 原有架构：
```
Redis (实时) → 模拟数据生成 → 输出
```

#### 新架构：
```
查询类型判断 → 
├── 实时查询 → Redis 缓存
├── 短期查询 → 分桶存储 (按月分表)
├── 长期查询 → 统计数据 (按年分表)
└── 降级处理 → 智能模拟数据
```

### 6. 存储策略集成

#### 分桶存储集成：
- **集合命名**：`TelemeteringBucket_{DataSourceCategory}_{yyyyMM}`
- **数据源支持**：Zongzi(综自)、Peidian(配电)、Fukong(辅控)、ZXJC(在线监测)、Robot(机器人)
- **时间分片**：按小时分桶，每桶最多3600个测量点

#### 统计数据集成：
- **集合命名**：`TelemeteringStatisticsBucket_{Interval}_{yyyy}`
- **统计类型**：实时值、最大值、最小值、平均值、差值、累计值、标准差、中位数
- **时间间隔**：1分钟到30天的多种间隔

### 7. 性能优化

#### 查询优化：
- **时间范围计算**：精确计算查询时间窗口
- **数据源选择**：根据数据密度选择最优存储层
- **批量查询**：支持多遥测点并行查询
- **缓存策略**：保持Redis实时数据的快速访问

#### 错误处理：
- **分层降级**：实际数据 → 历史数据 → 模拟数据
- **异常隔离**：单个遥测点错误不影响整体查询
- **日志记录**：详细的错误日志和性能指标

## 兼容性保证

### 接口兼容性：
- 所有公共方法签名保持不变
- 返回数据格式完全兼容
- 现有调用代码无需修改

### 功能兼容性：
- 保留原有的模拟数据生成作为降级方案
- 保持原有的时间格式化和数据处理逻辑
- 维持原有的业务逻辑和计算规则

## 配置要求

### 依赖注入配置：
需要在 DI 容器中注册新的服务：
```csharp
services.AddScoped<ITelemeteringBucketQueryService, TelemeteringBucketQueryService>();
services.AddScoped<IMongoDbRepository<TelemeteringResult, Guid>, MongoDbRepository<TelemeteringResult, Guid>>();
services.AddScoped<IMongoDbRepository<TelemeteringStatisticsResult, Guid>, MongoDbRepository<TelemeteringStatisticsResult, Guid>>();
```

### 数据库配置：
确保 MongoDB 中存在相应的集合和索引：
- TelemeteringBucket 系列集合
- TelemeteringStatisticsBucket 系列集合
- 相应的复合索引

## 测试建议

### 单元测试：
- 测试各种查询类型的数据源选择逻辑
- 验证时间范围计算的准确性
- 测试错误处理和降级机制

### 集成测试：
- 验证与遥测存储系统的集成
- 测试不同数据源的查询性能
- 验证数据一致性和准确性

### 性能测试：
- 对比新旧实现的查询性能
- 测试大时间范围查询的响应时间
- 验证并发查询的稳定性

## 监控指标

建议添加以下监控指标：
- 各数据源的查询次数和响应时间
- 模拟数据生成的频率（作为数据质量指标）
- 查询错误率和降级率
- 缓存命中率和数据新鲜度

## 总结

本次修改成功将 TransformerMonitoringAppService 从简单的 Redis + 模拟数据架构升级为完整的遥测存储系统架构，实现了：

1. **数据源多样化**：支持实时、历史、统计多种数据源
2. **查询智能化**：根据查询类型自动选择最优数据源
3. **性能优化**：利用分桶存储和统计预计算提升查询效率
4. **可靠性增强**：完善的错误处理和降级机制
5. **架构现代化**：符合遥测存储系统的设计规范和最佳实践

修改后的服务能够提供更准确、更高效、更可靠的变压器监控数据查询功能，为上层业务应用提供强有力的数据支撑。
