@echo off
chcp 65001 >nul
title SOMS Redis Management Console

echo.
echo ========================================
echo        SOMS Redis Management Console
echo ========================================
echo.
echo Starting Redis Management Service...
echo Tip: Press 'q' or Ctrl+C to exit
echo Working Directory: %CD%
echo.

REM Check if project file exists
if not exist "RunRedis.csproj" (
    echo Error: RunRedis.csproj file not found
    echo Please ensure you are running this script in the correct directory
    echo.
    pause
    exit /b 1
)

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: .NET runtime not found
    echo Please install .NET Core 3.1 or higher
    echo.
    pause
    exit /b 1
)

echo Environment check passed, starting application...
echo.

dotnet run --project RunRedis.csproj

echo.
echo Program exited
echo If you encounter issues, check log files: D:\SOMS\Logs\RunRedis-*.txt
echo.
echo Press any key to close window...
pause >nul
