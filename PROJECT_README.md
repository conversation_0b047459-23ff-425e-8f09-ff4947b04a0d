# SOMS (辅助监控系统) 项目说明文档

## 项目概述

SOMS (辅助监控系统) 是一个基于 .NET Core 3.1 的工业监控系统，主要用于电力系统的数据采集、监控、存储和分析。该系统采用分布式架构，支持多种通信协议，具备高可用性和可扩展性。

### 主要特性
- **多协议支持**: IEC104、TCP/IP、WebSocket 等
- **多数据源**: 综自、配电、辅控、在线监测、机器人巡检
- **实时数据处理**: 遥测、遥信、电度数据的实时采集和存储
- **分布式存储**: MongoDB + Redis + MySQL 混合存储架构
- **Web 管理界面**: 基于 ASP.NET Core MVC 的管理后台
- **守护程序**: 自动监控和重启服务
- **数据可视化**: 实时数据展示和历史数据查询

## 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Web API       │    │   数据服务      │
│  (MVC/Web)     │◄──►│  (ASP.NET Core) │◄──►│  (WPF Service)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   通信网关      │    │   数据存储      │    │   监控守护      │
│  (IEC104/TCP)   │    │ (MongoDB/Redis) │    │   (WatchDog)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈
- **后端框架**: .NET Core 3.1, ASP.NET Core
- **ORM**: Entity Framework Core
- **数据库**: MongoDB, Redis, MySQL, SQL Server
- **通信协议**: IEC104, TCP/IP, WebSocket
- **前端**: ASP.NET Core MVC, WPF
- **依赖注入**: Autofac
- **日志**: Serilog, log4net
- **定时任务**: Quartz.NET

## 项目结构

### 核心模块

#### 1. 数据监控服务器 (Yunda.SOMS.DataMonitoringServer)
- **位置**: `src/YunDa.Server/Yunda.ISAS.DataMonitoringServer/`
- **功能**: 核心数据采集和处理服务
- **特性**:
  - 实时数据采集和存储
  - 分桶存储策略
  - 统计计算
  - 数据压缩和优化

#### 2. Web 管理界面 (YunDa.SOMS.Web.MVC)
- **位置**: `src/YunDa.Web/YunDa.SOMS.Web.MVC/`
- **功能**: 系统管理后台
- **特性**:
  - 用户管理
  - 设备配置
  - 数据查询
  - 系统监控

#### 3. IEC104 通信服务
- **服务器**: `src/YunDa.Web/YunDa.ISAS.Iec104Server/`
- **客户端**: `src/YunDa.Web/YundDa.ISAS.Iec104Client/`
- **集成**: `src/YunDa.Server/Iec104ServerIntegration/`
- **功能**: IEC104 协议通信实现

#### 4. 运维调度通信网关 (OperationsMainSiteGatewayServer)
- **位置**: `OperationsMainSiteGatewayServer/`
- **功能**: 主站与子站之间的通信网关
- **特性**: 基于 DotNetty 的高性能网络通信

#### 5. 守护程序 (WatchDog)
- **位置**: `src/YunDa.Quick/WatchDog/`
- **功能**: 系统服务监控和自动重启
- **特性**:
  - 服务状态监控
  - 自动重启
  - 日志记录
  - 邮件告警

### 数据存储模块

#### MongoDB 服务 (RunMongoDB)
- **位置**: `src/YunDa.Quick/RunMongoDB/`
- **功能**: MongoDB 数据库管理服务
- **特性**:
  - 自动启动和监控
  - 性能优化
  - 健康检查
  - 备份管理

#### Redis 服务 (RunRedis)
- **位置**: `src/YunDa.Quick/RunRedis/`
- **功能**: Redis 缓存服务管理
- **特性**:
  - 连接池管理
  - 性能监控
  - 自动备份
  - 故障恢复

### 工具模块

#### 工具库 (ToolLibrary)
- **位置**: `src/YunDa.Util/ToolLibrary/`
- **功能**: 通用工具类和扩展方法

#### 视频监控适配器 (VideoSurveillanceAdapter)
- **位置**: `src/YunDa.Util/VideoSurveillanceAdapter/`
- **功能**: 视频监控系统集成

#### DotNetty 助手 (DotNettyHelper)
- **位置**: `src/YunDa.Util/DotNettyHelper/`
- **功能**: 网络通信框架封装

## 数据模型

### 核心实体

#### 遥测数据模型
```csharp
public class TelemeteringModel
{
    public float ResultValue { get; set; }           // 测量结果值
    public float LastResultValue { get; set; }       // 上次结果值
    public DateTime ResultTime { get; set; }         // 结果时间
    public string Name { get; set; }                 // 遥测名称
    public string Unit { get; set; }                 // 单位
    public Guid? EquipmentInfoId { get; set; }       // 设备ID
    public int? DataSourceCategory { get; set; }     // 数据源类型
}
```

#### 数据源类型
- **综自** (Zongzi = 0): 综合自动化系统
- **配电** (Peidian = 1): 配电系统
- **辅控** (Fukong = 2): 辅助控制系统
- **在线监测** (ZXJC = 3): 在线监测系统
- **机器人** (Robot = 4): 机器人巡检系统

### 存储策略

#### 实时数据存储
- **集合命名**: `TelemeteringModel_{DataSourceCategory}{yyyyMMdd}`
- **分桶策略**: 按小时分桶，每桶最多3600个测量点
- **索引**: TelemeteringConfigurationId, ResultTime, EquipmentInfoId

#### 统计数据存储
- **集合命名**: `TelemeteringStatisticsResult_{Interval}_{yyyy}`
- **统计类型**: 最大值、最小值、平均值、累计值等
- **时间间隔**: 1分钟到30天

## 配置说明

### 数据库配置

#### MongoDB 配置
```json
{
  "MongoDBSetting": {
    "Host": "127.0.0.1",
    "Port": "37017",
    "DatabaseName": "soms_mongodb",
    "IsAuth": "false",
    "UserName": "isasAdmin",
    "PassWord": "******"
  }
}
```

#### Redis 配置
```json
{
  "RedisSetting": {
    "Host": "127.0.0.1",
    "Port": "36379",
    "Auth": "yunda123",
    "DefaultDatabaseIndex": "0"
  }
}
```

#### MySQL 配置
```json
{
  "MysqlSetting": "server=127.0.0.1;port=3306;uid=root;pwd=******;Database=soms_sys_db;SslMode=none;"
}
```

### 系统配置

#### 守护程序配置
```json
{
  "SysBaseConfig": {
    "MainWindowDelayTime": 30,
    "SysAttachmentFolder": "D:\\SOMS\\Data\\SysAttachment"
  }
}
```

## 部署指南

### 环境要求
- **操作系统**: Windows 10/11, Windows Server 2016+
- **.NET Runtime**: .NET Core 3.1 Runtime
- **数据库**: MongoDB 4.4+, Redis 6.0+, MySQL 8.0+
- **内存**: 最低 8GB，推荐 16GB+
- **磁盘**: 最低 100GB 可用空间

### 安装步骤

#### 1. 环境准备
```bash
# 安装 .NET Core 3.1 Runtime
# 下载并安装 MongoDB
# 下载并安装 Redis
# 安装 MySQL
```

#### 2. 数据库初始化
```bash
# 创建 MongoDB 数据库
# 创建 Redis 实例
# 创建 MySQL 数据库和用户
```

#### 3. 服务部署
```bash
# 发布应用程序
dotnet publish -c Release -r win-x64 --self-contained

# 安装 Windows 服务
sc create "SOMS Data Service" binPath="C:\Path\To\Yunda.SOMS.DataMonitoringServer.exe"
sc start "SOMS Data Service"
```

#### 4. 配置服务
- 修改 `appsettings.json` 中的连接字符串
- 配置日志路径
- 设置数据存储路径

### 服务启动顺序
1. MongoDB 服务
2. Redis 服务
3. 数据监控服务器
4. Web API 服务
5. 守护程序

## 开发指南

### 项目结构说明

#### 分层架构
- **Application Layer**: 应用服务层，处理业务逻辑
- **Domain Layer**: 领域层，包含实体和领域服务
- **Infrastructure Layer**: 基础设施层，包含数据访问和外部服务
- **Web Layer**: Web 层，包含控制器和视图

#### 命名规范
- **项目命名**: `YunDa.SOMS.{ModuleName}`
- **命名空间**: `YunDa.ISAS.{ModuleName}`
- **类命名**: PascalCase
- **方法命名**: PascalCase
- **变量命名**: camelCase

### 开发环境设置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd server
```

#### 2. 还原依赖
```bash
dotnet restore
```

#### 3. 构建项目
```bash
dotnet build
```

#### 4. 运行测试
```bash
dotnet test
```

### 代码规范

#### C# 编码规范
- 使用 C# 8.0+ 特性
- 遵循 Microsoft C# 编码约定
- 使用 async/await 进行异步编程
- 使用依赖注入管理对象生命周期

#### 数据库规范
- 使用 Entity Framework Core 进行数据访问
- 遵循 Code First 开发模式
- 使用迁移管理数据库架构变更

## 监控和维护

### 日志管理
- **日志框架**: Serilog, log4net
- **日志级别**: Debug, Information, Warning, Error, Fatal
- **日志存储**: 文件 + 数据库
- **日志轮转**: 按日期和大小轮转

### 性能监控
- **CPU 使用率**: 监控系统 CPU 使用情况
- **内存使用**: 监控内存占用和 GC 情况
- **数据库性能**: 监控查询性能和连接数
- **网络性能**: 监控网络延迟和吞吐量

### 备份策略
- **MongoDB**: 每日全量备份，每小时增量备份
- **Redis**: 实时持久化，每日备份
- **MySQL**: 每日全量备份
- **配置文件**: 版本控制管理

### 故障处理
- **服务异常**: 自动重启机制
- **数据库连接**: 连接池管理和重试机制
- **网络异常**: 断线重连和心跳检测
- **数据异常**: 数据校验和修复机制

## 常见问题

### 1. 服务启动失败
**问题**: 数据监控服务器无法启动
**解决方案**:
- 检查数据库连接配置
- 确认端口未被占用
- 检查日志文件中的错误信息

### 2. 数据采集异常
**问题**: IEC104 数据采集中断
**解决方案**:
- 检查网络连接
- 验证设备配置
- 重启通信服务

### 3. 性能问题
**问题**: 系统响应缓慢
**解决方案**:
- 检查数据库性能
- 优化查询语句
- 增加系统资源

### 4. 存储空间不足
**问题**: 磁盘空间不足
**解决方案**:
- 清理历史数据
- 调整数据保留策略
- 扩展存储空间

## 版本历史

### v2.2.001 (当前版本)
- 优化数据存储性能
- 增强 IEC104 通信稳定性
- 改进守护程序功能
- 修复已知问题

### v2.1.201
- 初始版本发布
- 基础功能实现
- 多协议支持

## 技术支持

### 联系方式
- **开发团队**: 成都交大运达电气股份有限公司研发部
- **技术支持**: 请联系系统管理员
- **文档更新**: 定期更新，请关注最新版本

### 相关文档
- [IEC104 协议规范](https://webstore.iec.ch/publication/60255)
- [MongoDB 官方文档](https://docs.mongodb.com/)
- [Redis 官方文档](https://redis.io/documentation)
- [.NET Core 官方文档](https://docs.microsoft.com/en-us/dotnet/core/)

---

**注意**: 本文档会随着系统更新而持续维护，请确保使用最新版本。 