using Abp.Authorization;
using Abp.Domain.Repositories;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using ToolLibrary.LogHelper;
using YunDa.ISAS.Application.Core.SwaggerHelper;
using YunDa.ISAS.DataTransferObject;
using YunDa.ISAS.Entities.DataMonitoring;
using YunDa.ISAS.Entities.GeneralInformation;
using YunDa.ISAS.MongoDB.Repositories;
using YunDa.ISAS.Redis.Repositories;
using YunDa.SOMS.DataTransferObject.DataMonitoring.EnergyManagementDto;
using YunDa.SOMS.DataTransferObject.GeneralInformation.EquipmentLiveDataDto;
using YunDa.SOMS.Entities.DataMonitoring;
using Yunda.SOMS.MongoDB.Entities.DataMonitoring;
using Yunda.ISAS.MongoDB.Entities.DataMonitoring;

namespace YunDa.ISAS.Application.DataMonitoring.EnergyManagement
{
    /// <summary>
    /// 变压器监视服务，提供变压器相关电气参数的查询功能
    /// </summary>
    public class TransformerMonitoringAppService : ITransformerMonitoringAppService
    {
        private readonly string _telemeteringModelListRediskey = "telemeteringModelList";

        /// <summary>
        /// 遥测数据实时库
        /// </summary>
        private readonly IRedisRepository<TelemeteringModel, string> _telemeteringModelListRedis;

        /// <summary>
        /// 设备信息仓储
        /// </summary>
        private readonly IRepository<EquipmentInfo, Guid> _equipmentInfoRepository;

        /// <summary>
        /// 能耗设备仓储
        /// </summary>
        private readonly IRepository<EnergyConsumptionDevice, Guid> _energyDeviceRepository;

        /// <summary>
        /// 能耗配置仓储
        /// </summary>
        private readonly IRepository<EnergyConsumptionConfig, Guid> _configRepository;

        /// <summary>
        /// MongoDB存储库
        /// </summary>
        private readonly IMongoDbRepository<BsonDocument, Guid> _mongoRepository;

        /// <summary>
        /// 遥测分桶查询服务
        /// </summary>
        private readonly ITelemeteringBucketQueryService _bucketQueryService;

        /// <summary>
        /// 遥测结果仓储
        /// </summary>
        private readonly IMongoDbRepository<TelemeteringResult, Guid> _telemeteringResultRepository;

        /// <summary>
        /// 遥测统计结果仓储
        /// </summary>
        private readonly IMongoDbRepository<TelemeteringStatisticsResult, Guid> _statisticsRepository;

        // 缓存超时时间（秒）
        private TimeSpan CACHE_TIMEOUT = TimeSpan.FromMinutes(5);

        public TransformerMonitoringAppService(
             IRedisRepository<TelemeteringModel, string> telemeteringModelListRedis,
             IRepository<EquipmentInfo, Guid> equipmentInfoRepository,
             IRepository<EnergyConsumptionDevice, Guid> energyDeviceRepository,
             IRepository<EnergyConsumptionConfig, Guid> configRepository,
             IMongoDbRepository<BsonDocument, Guid> mongoRepository,
             ITelemeteringBucketQueryService bucketQueryService,
             IMongoDbRepository<TelemeteringResult, Guid> telemeteringResultRepository,
             IMongoDbRepository<TelemeteringStatisticsResult, Guid> statisticsRepository)
        {
            _telemeteringModelListRedis = telemeteringModelListRedis;
            _equipmentInfoRepository = equipmentInfoRepository;
            _energyDeviceRepository = energyDeviceRepository;
            _configRepository = configRepository;
            _mongoRepository = mongoRepository;
            _bucketQueryService = bucketQueryService;
            _telemeteringResultRepository = telemeteringResultRepository;
            _statisticsRepository = statisticsRepository;
        }

        [HttpGet]
        [ShowApi]
        [Description("获取高压侧A、B、C相相电压")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<HighSideVoltageData>> GetHighSidePhaseVoltage(Guid deviceId, RealTimePowerTypeEnum realTimePowerType)
        {
            var rst = new RequestResult<HighSideVoltageData>();

            try
            {
                // 1. 检查设备是否存在
                var device = _energyDeviceRepository.GetAllIncluding(t => t.EquipmentInfo).FirstOrDefault(t => t.Id == deviceId);
                if (device == null || !device.IsActive)
                {
                    rst.Message = "未找到指定的变压器设备或设备已禁用";
                    return rst;
                }

                // 2. 获取能源消耗配置，包含遥测配置
                var configs = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.IsActive &&
                           t.TelemeteringConfiguration != null &&
                           t.TelemeteringConfiguration.IsActive &&
                           t.EnergyConsumptionDeviceId == deviceId)
                    .ToList();

                // 3. 筛选高压侧A、B、C相相电压的遥测配置
                var phaseAVoltageConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("高压侧A相电压") ||
                    t.TelemeteringConfiguration.Name.Contains("高压A相") ||
                    t.TelemeteringConfiguration.Name.Contains("高压侧相电压A")).ToList();

                var phaseBVoltageConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("高压侧B相电压") ||
                    t.TelemeteringConfiguration.Name.Contains("高压B相") ||
                    t.TelemeteringConfiguration.Name.Contains("高压侧相电压B")).ToList();

                var phaseCVoltageConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("高压侧C相电压") ||
                    t.TelemeteringConfiguration.Name.Contains("高压C相") ||
                    t.TelemeteringConfiguration.Name.Contains("高压侧相电压C")).ToList();

                // 4. 根据RealTimePowerType处理数据
                var phaseAVoltageData = new List<FloatTimeOutput>();
                var phaseBVoltageData = new List<FloatTimeOutput>();
                var phaseCVoltageData = new List<FloatTimeOutput>();

                // 设置时间间隔和点数
                DateTime currentTime = DateTime.Now;
                // 调整当前时间，前移一段时间，确保查询到的都是有效数据
                currentTime = currentTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(realTimePowerType));
                int pointCount;
                TimeSpan interval;

                // 根据不同的数据类型调整时间间隔和点数
                GetIntervalAndPointCount(realTimePowerType, out interval, out pointCount);

                // 获取时间格式化字符串
                string timeFormat = GetTimeFormat(realTimePowerType);

                // 计算查询时间范围
                DateTime startTime = currentTime.AddSeconds(-1 * (pointCount - 1) * interval.TotalSeconds);
                DateTime endTime = currentTime;

                // 5. 使用新的遥测存储系统获取数据
                // 6. 生成A相电压数据
                phaseAVoltageData = await GetTelemetryVoltageData(phaseAVoltageConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 7. 生成B相电压数据
                phaseBVoltageData = await GetTelemetryVoltageData(phaseBVoltageConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 8. 生成C相电压数据
                phaseCVoltageData = await GetTelemetryVoltageData(phaseCVoltageConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 9. 构建并返回结果
                rst.ResultData = new HighSideVoltageData
                {
                    PhaseAVoltage = phaseAVoltageData,
                    PhaseBVoltage = phaseBVoltageData,
                    PhaseCVoltage = phaseCVoltageData,
                    TimeIntervalType = realTimePowerType
                };

                rst.Flag = true;
                rst.Message = "获取高压侧相电压数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取高压侧相电压数据", ex);
                rst.Message = "获取高压侧相电压数据失败: " + ex.Message;
            }

            return rst;
        }

        [HttpGet]
        [ShowApi]
        [Description("获取高压侧A、B、C相相电流")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<HighSideCurrentData>> GetHighSidePhaseCurrent(Guid deviceId, RealTimePowerTypeEnum realTimePowerType)
        {
            var rst = new RequestResult<HighSideCurrentData>();

            try
            {
                // 1. 检查设备是否存在
                var device = _energyDeviceRepository.GetAllIncluding(t => t.EquipmentInfo).FirstOrDefault(t => t.Id == deviceId);

                if (device == null || !device.IsActive)
                {
                    rst.Message = "未找到指定的变压器设备或设备已禁用";
                    return rst;
                }

                // 2. 获取能源消耗配置，包含遥测配置
                var configs = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.IsActive &&
                           t.TelemeteringConfiguration != null &&
                           t.TelemeteringConfiguration.IsActive &&
                           t.EnergyConsumptionDeviceId == deviceId)
                    .ToList();

                // 3. 筛选高压侧A、B、C相相电流的遥测配置
                var phaseACurrentConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("高压侧A相电流") ||
                    t.TelemeteringConfiguration.Name.Contains("高压A相电流") ||
                    t.TelemeteringConfiguration.Name.Contains("高压侧相电流A")).ToList();

                var phaseBCurrentConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("高压侧B相电流") ||
                    t.TelemeteringConfiguration.Name.Contains("高压B相电流") ||
                    t.TelemeteringConfiguration.Name.Contains("高压侧相电流B")).ToList();

                var phaseCCurrentConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("高压侧C相电流") ||
                    t.TelemeteringConfiguration.Name.Contains("高压C相电流") ||
                    t.TelemeteringConfiguration.Name.Contains("高压侧相电流C")).ToList();

                // 4. 根据RealTimePowerType处理数据
                var phaseACurrentData = new List<FloatTimeOutput>();
                var phaseBCurrentData = new List<FloatTimeOutput>();
                var phaseCCurrentData = new List<FloatTimeOutput>();

                // 设置时间间隔和点数
                DateTime currentTime = DateTime.Now;
                // 调整当前时间，前移一段时间，确保查询到的都是有效数据
                currentTime = currentTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(realTimePowerType));
                int pointCount;
                TimeSpan interval;

                // 根据不同的数据类型调整时间间隔和点数
                GetIntervalAndPointCount(realTimePowerType, out interval, out pointCount);

                // 获取时间格式化字符串
                string timeFormat = GetTimeFormat(realTimePowerType);

                // 计算查询时间范围
                DateTime startTime = currentTime.AddSeconds(-1 * (pointCount - 1) * interval.TotalSeconds);
                DateTime endTime = currentTime;

                // 5. 使用新的遥测存储系统获取数据
                // 6. 生成A相电流数据
                phaseACurrentData = await GetTelemetryCurrentData(phaseACurrentConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 7. 生成B相电流数据
                phaseBCurrentData = await GetTelemetryCurrentData(phaseBCurrentConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 8. 生成C相电流数据
                phaseCCurrentData = await GetTelemetryCurrentData(phaseCCurrentConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 9. 构建并返回结果
                rst.ResultData = new HighSideCurrentData
                {
                    PhaseACurrent = phaseACurrentData,
                    PhaseBCurrent = phaseBCurrentData,
                    PhaseCCurrent = phaseCCurrentData,
                    TimeIntervalType = realTimePowerType
                };

                rst.Flag = true;
                rst.Message = "获取高压侧相电流数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取高压侧相电流数据", ex);
                rst.Message = "获取高压侧相电流数据失败: " + ex.Message;
            }

            return rst;
        }

        [HttpGet]
        [ShowApi]
        [Description("获取低压侧F1、F2线电压")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<LowSideVoltageData>> GetLowSideLineVoltage(Guid deviceId, RealTimePowerTypeEnum realTimePowerType)
        {
            var rst = new RequestResult<LowSideVoltageData>();

            try
            {
                // 1. 检查设备是否存在
                var device = _energyDeviceRepository.GetAllIncluding(t => t.EquipmentInfo).FirstOrDefault(t => t.Id == deviceId);

                if (device == null || !device.IsActive)
                {
                    rst.Message = "未找到指定的变压器设备或设备已禁用";
                    return rst;
                }

                // 2. 获取能源消耗配置，包含遥测配置
                var configs = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.IsActive &&
                           t.TelemeteringConfiguration != null &&
                           t.TelemeteringConfiguration.IsActive &&
                           t.EnergyConsumptionDeviceId == deviceId)
                    .ToList();

                // 3. 筛选低压侧F1、F2线电压的遥测配置
                var f1VoltageConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("低压侧F1线电压") ||
                    t.TelemeteringConfiguration.Name.Contains("F1线电压") ||
                    t.TelemeteringConfiguration.Name.Contains("低压F1") ||
                    t.TelemeteringConfiguration.Name.Contains("低压侧F1")).ToList();

                var f2VoltageConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("低压侧F2线电压") ||
                    t.TelemeteringConfiguration.Name.Contains("F2线电压") ||
                    t.TelemeteringConfiguration.Name.Contains("低压F2") ||
                    t.TelemeteringConfiguration.Name.Contains("低压侧F2")).ToList();

                // 4. 根据RealTimePowerType处理数据
                var lineF1VoltageData = new List<FloatTimeOutput>();
                var lineF2VoltageData = new List<FloatTimeOutput>();

                // 设置时间间隔和点数
                DateTime currentTime = DateTime.Now;
                // 调整当前时间，前移一段时间，确保查询到的都是有效数据
                currentTime = currentTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(realTimePowerType));
                int pointCount;
                TimeSpan interval;

                // 根据不同的数据类型调整时间间隔和点数
                GetIntervalAndPointCount(realTimePowerType, out interval, out pointCount);

                // 获取时间格式化字符串
                string timeFormat = GetTimeFormat(realTimePowerType);

                // 计算查询时间范围
                DateTime startTime = currentTime.AddSeconds(-1 * (pointCount - 1) * interval.TotalSeconds);
                DateTime endTime = currentTime;

                // 5. 使用新的遥测存储系统获取数据
                // 6. 生成F1线电压数据
                lineF1VoltageData = await GetTelemetryVoltageData(f1VoltageConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 7. 生成F2线电压数据
                lineF2VoltageData = await GetTelemetryVoltageData(f2VoltageConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 8. 构建并返回结果
                rst.ResultData = new LowSideVoltageData
                {
                    F1Voltage = lineF1VoltageData,
                    F2Voltage = lineF2VoltageData,
                    TimeIntervalType = realTimePowerType
                };

                rst.Flag = true;
                rst.Message = "获取低压侧线电压数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取低压侧线电压数据", ex);
                rst.Message = "获取低压侧线电压数据失败: " + ex.Message;
            }

            return rst;
        }

        [HttpGet]
        [ShowApi]
        [Description("获取低压侧F1、F2线电流")]
        [AbpAllowAnonymous]
        public async Task<RequestResult<LowSideCurrentData>> GetLowSideLineCurrent(Guid deviceId, RealTimePowerTypeEnum realTimePowerType)
        {
            var rst = new RequestResult<LowSideCurrentData>();
            if (realTimePowerType == RealTimePowerTypeEnum.RealTime)
            {
                rst.Message = "";
                return rst;
            }
            try
            {

                // 1. 检查设备是否存在
                var device = _energyDeviceRepository.GetAllIncluding(t => t.EquipmentInfo).FirstOrDefault(t => t.Id == deviceId);

                if (device == null || !device.IsActive)
                {
                    rst.Message = "未找到指定的变压器设备或设备已禁用";
                    return rst;
                }

                // 2. 获取能源消耗配置，包含遥测配置
                var configs = _configRepository.GetAllIncluding(t => t.TelemeteringConfiguration)
                    .Where(t => t.IsActive &&
                           t.TelemeteringConfiguration != null &&
                           t.TelemeteringConfiguration.IsActive &&
                           t.EnergyConsumptionDeviceId == deviceId)
                    .ToList();

                // 3. 筛选低压侧F1、F2线电流的遥测配置
                var f1CurrentConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("低压侧F1线电流") ||
                    t.TelemeteringConfiguration.Name.Contains("F1线电流") ||
                    t.TelemeteringConfiguration.Name.Contains("低压F1电流") ||
                    t.TelemeteringConfiguration.Name.Contains("F1回路电流")).ToList();

                var f2CurrentConfigs = configs.Where(t =>
                    t.TelemeteringConfiguration.Name.Contains("低压侧F2线电流") ||
                    t.TelemeteringConfiguration.Name.Contains("F2线电流") ||
                    t.TelemeteringConfiguration.Name.Contains("低压F2电流") ||
                    t.TelemeteringConfiguration.Name.Contains("F2回路电流")).ToList();

                // 4. 根据RealTimePowerType处理数据
                var f1CurrentData = new List<FloatTimeOutput>();
                var f2CurrentData = new List<FloatTimeOutput>();

                // 设置时间间隔和点数
                DateTime currentTime = DateTime.Now;
                // 调整当前时间，前移一段时间，确保查询到的都是有效数据
                currentTime = currentTime.Subtract(TimeAdjustmentHelper.GetEndTimeSpanInterval(realTimePowerType));
                int pointCount;
                TimeSpan interval;

                // 根据不同的数据类型调整时间间隔和点数
                GetIntervalAndPointCount(realTimePowerType, out interval, out pointCount);

                // 获取时间格式化字符串
                string timeFormat = GetTimeFormat(realTimePowerType);

                // 计算查询时间范围
                DateTime startTime = currentTime.AddSeconds(-1 * (pointCount - 1) * interval.TotalSeconds);
                DateTime endTime = currentTime;

                // 5. 使用新的遥测存储系统获取数据
                // 6. 生成F1线电流数据
                f1CurrentData = await GetTelemetryCurrentData(f1CurrentConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 7. 生成F2线电流数据
                f2CurrentData = await GetTelemetryCurrentData(f2CurrentConfigs,
                    startTime, endTime, pointCount, interval, realTimePowerType, timeFormat);

                // 8. 构建并返回结果
                rst.ResultData = new LowSideCurrentData
                {
                    F1Current = f1CurrentData,
                    F2Current = f2CurrentData,
                    TimeIntervalType = realTimePowerType
                };

                rst.Flag = true;
                rst.Message = "获取低压侧线电流数据成功";
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), "获取低压侧线电流数据", ex);
                rst.Message = "获取低压侧线电流数据失败: " + ex.Message;
            }

            return rst;
        }

        #region 私有辅助方法

        /// <summary>
        /// 获取时间间隔和数据点数量
        /// </summary>
        private void GetIntervalAndPointCount(RealTimePowerTypeEnum powerType, out TimeSpan interval, out int pointCount)
        {
            // 根据不同的数据类型调整时间间隔和点数
            switch (powerType)
            {
                case RealTimePowerTypeEnum.Hourly:
                    // 小时数据：每分钟一个点，显示最近60分钟
                    interval = TimeSpan.FromMinutes(1);
                    pointCount = 60;
                    break;
                case RealTimePowerTypeEnum.Daily:
                    // 天数据：每小时一个点，显示最近24小时
                    interval = TimeSpan.FromHours(1);
                    pointCount = 24;
                    break;
                case RealTimePowerTypeEnum.Weekly:
                    // 周数据：每天一个点，显示最近7天
                    interval = TimeSpan.FromDays(1);
                    pointCount = 7;
                    break;
                case RealTimePowerTypeEnum.Monthly:
                    // 月数据：每天一个点，显示最近30天
                    interval = TimeSpan.FromDays(1);
                    pointCount = 30;
                    break;
                case RealTimePowerTypeEnum.Yearly:
                    // 年数据：每月一个点，显示最近12个月
                    interval = TimeSpan.FromDays(30);
                    pointCount = 12;
                    break;
                default:
                    interval = TimeSpan.FromSeconds(1);
                    pointCount = 60;
                    break;
            }
        }

        /// <summary>
        /// 获取时间格式化字符串
        /// </summary>
        private string GetTimeFormat(RealTimePowerTypeEnum powerType)
        {
            switch (powerType)
            {
                case RealTimePowerTypeEnum.RealTime:
                    return "HH:mm:ss";
                case RealTimePowerTypeEnum.Hourly:
                    return "HH:mm";
                case RealTimePowerTypeEnum.Daily:
                    return "MM-dd HH:00";
                case RealTimePowerTypeEnum.Weekly:
                    return "MM-dd dddd";
                case RealTimePowerTypeEnum.Monthly:
                    return "yyyy-MM-dd";
                case RealTimePowerTypeEnum.Yearly:
                    return "yyyy-MM";
                default:
                    return "yyyy-MM-dd HH:mm:ss";
            }
        }

        /// <summary>
        /// 格式化时间字符串
        /// </summary>
        private string GetFormattedTimeString(DateTime time, string format)
        {
            // 如果格式中包含dddd，表示需要显示中文星期几
            if (format.Contains("dddd"))
            {
                string formattedDate = time.ToString(format.Replace("dddd", "").Trim());
                string chineseWeekday = TimeAdjustmentHelper.GetChineseDayOfWeek(time);
                return $"{formattedDate} {chineseWeekday}";
            }
            return time.ToString(format);
        }

        /// <summary>
        /// 使用遥测存储系统获取电压数据
        /// </summary>
        private async Task<List<FloatTimeOutput>> GetTelemetryVoltageData(
            List<EnergyConsumptionConfig> configs,
            DateTime startTime,
            DateTime endTime,
            int pointCount,
            TimeSpan interval,
            RealTimePowerTypeEnum powerType,
            string timeFormat = "yyyy-MM-dd HH:mm:ss")
        {
            var voltageData = new List<FloatTimeOutput>();

            if (!configs.Any())
            {
                // 如果没有配置，生成模拟数据
                return await GenerateSimulatedVoltageData(startTime, endTime, pointCount, interval, powerType, 10000, timeFormat);
            }

            try
            {
                // 获取遥测配置ID列表
                var telemetryIds = configs.Select(t => t.TelemeteringConfiguration?.Id)
                    .Where(id => id.HasValue)
                    .Select(id => id.Value)
                    .ToList();

                if (!telemetryIds.Any())
                {
                    return await GenerateSimulatedVoltageData(startTime, endTime, pointCount, interval, powerType, 10000, timeFormat);
                }

                // 根据查询类型选择数据源
                var telemetryData = await GetTelemetryDataByType(telemetryIds, startTime, endTime, powerType);

                if (telemetryData.Any())
                {
                    // 使用实际数据
                    voltageData = await ProcessRealTelemetryData(telemetryData, startTime, endTime, pointCount, interval, powerType, timeFormat);
                }
                else
                {
                    // 没有实际数据，生成模拟数据
                    voltageData = await GenerateSimulatedVoltageData(startTime, endTime, pointCount, interval, powerType, 10000, timeFormat);
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"获取遥测电压数据失败: {ex.Message}", ex);
                // 出错时生成模拟数据
                voltageData = await GenerateSimulatedVoltageData(startTime, endTime, pointCount, interval, powerType, 10000, timeFormat);
            }

            return voltageData;
        }

        /// <summary>
        /// 使用遥测存储系统获取电流数据
        /// </summary>
        private async Task<List<FloatTimeOutput>> GetTelemetryCurrentData(
            List<EnergyConsumptionConfig> configs,
            DateTime startTime,
            DateTime endTime,
            int pointCount,
            TimeSpan interval,
            RealTimePowerTypeEnum powerType,
            string timeFormat = "yyyy-MM-dd HH:mm:ss")
        {
            var currentData = new List<FloatTimeOutput>();

            if (!configs.Any())
            {
                // 如果没有配置，生成模拟数据
                return await GenerateSimulatedCurrentData(startTime, endTime, pointCount, interval, powerType, 100, timeFormat);
            }

            try
            {
                // 获取遥测配置ID列表
                var telemetryIds = configs.Select(t => t.TelemeteringConfiguration?.Id)
                    .Where(id => id.HasValue)
                    .Select(id => id.Value)
                    .ToList();

                if (!telemetryIds.Any())
                {
                    return await GenerateSimulatedCurrentData(startTime, endTime, pointCount, interval, powerType, 100, timeFormat);
                }

                // 根据查询类型选择数据源
                var telemetryData = await GetTelemetryDataByType(telemetryIds, startTime, endTime, powerType);

                if (telemetryData.Any())
                {
                    // 使用实际数据
                    currentData = await ProcessRealTelemetryData(telemetryData, startTime, endTime, pointCount, interval, powerType, timeFormat);
                }
                else
                {
                    // 没有实际数据，生成模拟数据
                    currentData = await GenerateSimulatedCurrentData(startTime, endTime, pointCount, interval, powerType, 100, timeFormat);
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"获取遥测电流数据失败: {ex.Message}", ex);
                // 出错时生成模拟数据
                currentData = await GenerateSimulatedCurrentData(startTime, endTime, pointCount, interval, powerType, 100, timeFormat);
            }

            return currentData;
        }

        /// <summary>
        /// 获取数据点的时间点
        /// </summary>
        private DateTime GetPointTime(DateTime currentTime, int index, TimeSpan interval, RealTimePowerTypeEnum powerType)
        {
            DateTime pointTime = currentTime.AddSeconds(-1 * index * interval.TotalSeconds);

            // 根据不同的数据类型对时间进行对齐
            switch (powerType)
            {
                case RealTimePowerTypeEnum.RealTime:
                    // 对齐到秒
                    pointTime = new DateTime(pointTime.Year, pointTime.Month, pointTime.Day, pointTime.Hour, pointTime.Minute, pointTime.Second);
                    break;
                case RealTimePowerTypeEnum.Hourly:
                    // 对齐到分钟
                    pointTime = new DateTime(pointTime.Year, pointTime.Month, pointTime.Day, pointTime.Hour, pointTime.Minute, 0);
                    break;
                case RealTimePowerTypeEnum.Daily:
                    // 对齐到小时
                    pointTime = new DateTime(pointTime.Year, pointTime.Month, pointTime.Day, pointTime.Hour, 0, 0);
                    break;
                case RealTimePowerTypeEnum.Weekly:
                case RealTimePowerTypeEnum.Monthly:
                    // 对齐到天
                    pointTime = new DateTime(pointTime.Year, pointTime.Month, pointTime.Day, 0, 0, 0);
                    break;
                case RealTimePowerTypeEnum.Yearly:
                    // 对齐到月
                    pointTime = new DateTime(pointTime.Year, pointTime.Month, 1, 0, 0, 0);
                    break;
                default:
                    // 默认精确到秒
                    break;
            }

            return pointTime;
        }

        /// <summary>
        /// 根据查询类型获取遥测数据
        /// </summary>
        private async Task<List<TelemeteringDataPoint>> GetTelemetryDataByType(
            List<Guid> telemetryIds,
            DateTime startTime,
            DateTime endTime,
            RealTimePowerTypeEnum powerType)
        {
            var allData = new List<TelemeteringDataPoint>();

            foreach (var telemetryId in telemetryIds)
            {
                try
                {
                    List<TelemeteringDataPoint> data = null;

                    // 根据查询类型选择数据源
                    switch (powerType)
                    {
                        case RealTimePowerTypeEnum.RealTime:
                            // 实时数据：从Redis获取最新数据
                            data = await GetRealTimeDataFromRedis(telemetryId);
                            break;

                        case RealTimePowerTypeEnum.Hourly:
                        case RealTimePowerTypeEnum.Daily:
                            // 小时和日数据：从分桶存储查询
                            data = await _bucketQueryService.QueryRealTimeData(
                                telemetryId,
                                (int)DataSourceCategoryEnum.Zongzi, // 默认使用综自数据源
                                startTime,
                                endTime);
                            break;

                        case RealTimePowerTypeEnum.Weekly:
                        case RealTimePowerTypeEnum.Monthly:
                        case RealTimePowerTypeEnum.Yearly:
                            // 周、月、年数据：从统计数据查询
                            data = await GetStatisticsData(telemetryId, startTime, endTime, powerType);
                            break;

                        default:
                            // 默认从分桶存储查询
                            data = await _bucketQueryService.QueryRealTimeData(
                                telemetryId,
                                (int)DataSourceCategoryEnum.Zongzi,
                                startTime,
                                endTime);
                            break;
                    }

                    if (data != null && data.Any())
                    {
                        allData.AddRange(data);
                    }
                }
                catch (Exception ex)
                {
                    Log4Helper.Error(this.GetType(), $"获取遥测数据失败 - ID: {telemetryId}, 错误: {ex.Message}", ex);
                }
            }

            return allData.OrderBy(d => d.ResultTime).ToList();
        }

        /// <summary>
        /// 根据查询类型生成合适的数据波动范围
        /// </summary>
        private float GenerateVarianceByQueryType(Random random, RealTimePowerTypeEnum queryType)
        {
            // 不同的查询类型使用不同的波动范围，使数据更真实
            switch (queryType)
            {
                case RealTimePowerTypeEnum.RealTime:
                    // 实时数据波动较大，但也较随机
                    return (float)(random.NextDouble() * 0.05 - 0.025); // -2.5% 到 ****% 的浮动

                case RealTimePowerTypeEnum.Hourly:
                    // 小时级数据，有一定的波动趋势
                    // 增加时间依赖性波动，早晚用电量差异
                    double hourOfDay = DateTime.Now.TimeOfDay.TotalHours;
                    double hourlyBase = hourOfDay >= 8 && hourOfDay <= 20 ? 0.01 : -0.01; // 工作时间较高
                    return (float)(hourlyBase + (random.NextDouble() * 0.07 - 0.035)); // -3.5% 到 +3.5% 的浮动

                case RealTimePowerTypeEnum.Daily:
                    // 日级数据，波动跟随日常用电规律
                    hourOfDay = DateTime.Now.TimeOfDay.TotalHours;
                    // 白天用电高，晚上用电低的规律
                    // 模拟一天中的用电高峰：早晨(8-10)、中午(12-14)、晚上(18-20)
                    double dailyFactor;
                    if ((hourOfDay >= 8 && hourOfDay <= 10) ||
                        (hourOfDay >= 12 && hourOfDay <= 14) ||
                        (hourOfDay >= 18 && hourOfDay <= 20))
                    {
                        dailyFactor = 0.03; // 高峰时段
                    }
                    else if (hourOfDay >= 1 && hourOfDay <= 6)
                    {
                        dailyFactor = -0.05; // 深夜低谷
                    }
                    else
                    {
                        dailyFactor = 0.0; // 平常时段
                    }
                    return (float)(dailyFactor + (random.NextDouble() * 0.05 - 0.025));

                case RealTimePowerTypeEnum.Weekly:
                    // 周级数据，工作日比周末高
                    int dayOfWeek = (int)DateTime.Now.DayOfWeek;
                    // 周末(0和6)用电低，工作日用电高，周一、周五略有不同
                    double weekFactor;
                    if (dayOfWeek == 0 || dayOfWeek == 6)
                    {
                        weekFactor = -0.04; // 周末
                    }
                    else if (dayOfWeek == 1)
                    {
                        weekFactor = 0.01; // 周一略低
                    }
                    else if (dayOfWeek == 5)
                    {
                        weekFactor = 0.01; // 周五略低
                    }
                    else
                    {
                        weekFactor = 0.03; // 周二到周四高
                    }
                    return (float)(weekFactor + (random.NextDouble() * 0.06 - 0.03));

                case RealTimePowerTypeEnum.Monthly:
                    // 月度数据，月初月末和月中的差异
                    int dayOfMonth = DateTime.Now.Day;
                    int daysInMonth = DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month);

                    // 月初(1-5天)、月中(10-20天)、月末(最后5天)的不同模式
                    double monthFactor;
                    if (dayOfMonth <= 5)
                    {
                        monthFactor = -0.02; // 月初较低
                    }
                    else if (dayOfMonth >= daysInMonth - 5)
                    {
                        monthFactor = -0.01; // 月末较低
                    }
                    else if (dayOfMonth >= 10 && dayOfMonth <= 20)
                    {
                        monthFactor = 0.03; // 月中较高
                    }
                    else
                    {
                        monthFactor = 0.0; // 其他时间正常
                    }
                    return (float)(monthFactor + (random.NextDouble() * 0.07 - 0.035));

                case RealTimePowerTypeEnum.Yearly:
                    // 年级数据，季节性波动明显(夏冬高，春秋低)
                    int month = DateTime.Now.Month;
                    // 不同季节的用电特点
                    double yearFactor;
                    if (month == 12 || month <= 2)
                    {
                        yearFactor = 0.04; // 冬季(取暖)
                    }
                    else if (month >= 6 && month <= 8)
                    {
                        yearFactor = 0.06; // 夏季(制冷)
                    }
                    else if (month >= 3 && month <= 5)
                    {
                        yearFactor = -0.02; // 春季
                    }
                    else
                    {
                        yearFactor = -0.01; // 秋季
                    }
                    return (float)(yearFactor + (random.NextDouble() * 0.06 - 0.03));

                default:
                    return (float)(random.NextDouble() * 0.05 - 0.025);
            }
        }

        /// <summary>
        /// 从Redis获取实时数据
        /// </summary>
        private async Task<List<TelemeteringDataPoint>> GetRealTimeDataFromRedis(Guid telemetryId)
        {
            try
            {
                var redisKey = "telemeteringModelList_Zongzi";
                var telemeterings = await _telemeteringModelListRedis.HashSetGetAllAsync(redisKey);

                var telemetryData = telemeterings?.Where(t => t.Id == telemetryId).FirstOrDefault();
                if (telemetryData != null)
                {
                    return new List<TelemeteringDataPoint>
                    {
                        new TelemeteringDataPoint
                        {
                            Id = telemetryData.Id,
                            TelemeteringConfigurationId = telemetryData.Id,
                            ResultValue = telemetryData.ResultValue,
                            ResultTime = telemetryData.ResultTime,
                            Name = telemetryData.Name,
                            Unit = telemetryData.Unit,
                            EquipmentInfoName = telemetryData.EquipmentInfo.Name
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"从Redis获取实时数据失败: {ex.Message}", ex);
            }

            return new List<TelemeteringDataPoint>();
        }

        /// <summary>
        /// 获取统计数据
        /// </summary>
        private async Task<List<TelemeteringDataPoint>> GetStatisticsData(
            Guid telemetryId,
            DateTime startTime,
            DateTime endTime,
            RealTimePowerTypeEnum powerType)
        {
            try
            {
                // 根据查询类型映射到统计间隔
                var interval = MapPowerTypeToInterval(powerType);
                var statisticsType = StatisticsTypeEnum.Average; // 默认使用平均值

                var statisticsResults = await _bucketQueryService.QueryStatisticsData(
                    telemetryId,
                    interval,
                    startTime,
                    endTime,
                    statisticsType);

                // 转换为TelemeteringDataPoint
                return statisticsResults.Select(s => new TelemeteringDataPoint
                {
                    Id = s.TelemeteringConfigurationId,
                    TelemeteringConfigurationId = s.TelemeteringConfigurationId,
                    ResultValue = s.ResultValue,
                    ResultTime = s.StatisticsDateTime,
                    Name = s.Name,
                    Unit = s.Unit,
                    EquipmentInfoName = s.EquipmentInfoName
                }).ToList();
            }
            catch (Exception ex)
            {
                Log4Helper.Error(this.GetType(), $"获取统计数据失败: {ex.Message}", ex);
                return new List<TelemeteringDataPoint>();
            }
        }

        /// <summary>
        /// 将查询类型映射到统计间隔
        /// </summary>
        private FixedIntervalEnum MapPowerTypeToInterval(RealTimePowerTypeEnum powerType)
        {
            switch (powerType)
            {
                case RealTimePowerTypeEnum.Hourly:
                    return FixedIntervalEnum.Minute5;
                case RealTimePowerTypeEnum.Daily:
                    return FixedIntervalEnum.Hour1;
                case RealTimePowerTypeEnum.Weekly:
                    return FixedIntervalEnum.Day1;
                case RealTimePowerTypeEnum.Monthly:
                    return FixedIntervalEnum.Day1;
                case RealTimePowerTypeEnum.Yearly:
                    return FixedIntervalEnum.Day30;
                default:
                    return FixedIntervalEnum.Minute1;
            }
        }

        /// <summary>
        /// 处理实际遥测数据
        /// </summary>
        private async Task<List<FloatTimeOutput>> ProcessRealTelemetryData(
            List<TelemeteringDataPoint> telemetryData,
            DateTime startTime,
            DateTime endTime,
            int pointCount,
            TimeSpan interval,
            RealTimePowerTypeEnum powerType,
            string timeFormat)
        {
            var result = new List<FloatTimeOutput>();

            if (!telemetryData.Any())
            {
                return result;
            }

            // 按时间排序
            var sortedData = telemetryData.OrderBy(d => d.ResultTime).ToList();

            // 生成时间点序列
            var timePoints = new List<DateTime>();
            for (int i = 0; i < pointCount; i++)
            {
                var pointTime = GetPointTime(endTime, i, interval, powerType);
                timePoints.Add(pointTime);
            }
            timePoints = timePoints.OrderBy(t => t).ToList();

            // 为每个时间点找到最接近的数据或插值
            foreach (var timePoint in timePoints)
            {
                var value = GetValueAtTime(sortedData, timePoint);
                result.Add(new FloatTimeOutput
                {
                    Value = value,
                    Time = GetFormattedTimeString(timePoint, timeFormat)
                });
            }

            return result;
        }

        /// <summary>
        /// 获取指定时间点的数值（插值或最近值）
        /// </summary>
        private float GetValueAtTime(List<TelemeteringDataPoint> sortedData, DateTime targetTime)
        {
            if (!sortedData.Any())
                return 0;

            // 找到最接近的数据点
            var closestData = sortedData
                .OrderBy(d => Math.Abs((d.ResultTime - targetTime).TotalSeconds))
                .First();

            return closestData.ResultValue;
        }

        /// <summary>
        /// 生成模拟电压数据
        /// </summary>
        private async Task<List<FloatTimeOutput>> GenerateSimulatedVoltageData(
            DateTime startTime,
            DateTime endTime,
            int pointCount,
            TimeSpan interval,
            RealTimePowerTypeEnum powerType,
            float baseValue,
            string timeFormat)
        {
            return await GenerateSimulatedData(startTime, endTime, pointCount, interval, powerType, baseValue, timeFormat);
        }

        /// <summary>
        /// 生成模拟电流数据
        /// </summary>
        private async Task<List<FloatTimeOutput>> GenerateSimulatedCurrentData(
            DateTime startTime,
            DateTime endTime,
            int pointCount,
            TimeSpan interval,
            RealTimePowerTypeEnum powerType,
            float baseValue,
            string timeFormat)
        {
            return await GenerateSimulatedData(startTime, endTime, pointCount, interval, powerType, baseValue, timeFormat);
        }

        /// <summary>
        /// 生成模拟数据的通用方法
        /// </summary>
        private Task<List<FloatTimeOutput>> GenerateSimulatedData(
            DateTime startTime,
            DateTime endTime,
            int pointCount,
            TimeSpan interval,
            RealTimePowerTypeEnum powerType,
            float baseValue,
            string timeFormat)
        {
            var data = new List<FloatTimeOutput>();
            Random random = new Random((int)DateTime.Now.Ticks);

            // 生成时间点序列
            for (int i = 0; i < pointCount; i++)
            {
                DateTime pointTime = GetPointTime(endTime, i, interval, powerType);

                // 生成有波动的模拟数据
                float variance = GenerateVarianceByQueryType(random, powerType);
                float value = baseValue * (1 + variance);

                data.Add(new FloatTimeOutput
                {
                    Value = value,
                    Time = GetFormattedTimeString(pointTime, timeFormat)
                });
            }

            // 按时间排序
            data = data.OrderBy(d => DateTime.Parse(d.Time)).ToList();
            return Task.FromResult(data);
        }

        #endregion
    }
}
